# 速度笔锋功能集成指南

## 概述

本文档介绍了为 whiteboard 的 FreeDrawTool 新增的速度笔锋功能。该功能根据鼠标/触控移动速度动态调整线条宽度，实现类似真实毛笔的笔锋效果。

## 功能特点

1. **完全复用已有代码**：基于现有的 `OptimizedDrawingState` 和 `FreeDrawTool` 架构
2. **性能优化**：使用高效的速度计算和路径生成算法
3. **效果自然**：最粗线宽为设置值，根据速度平滑减少到最小 20% 宽度
4. **无缝集成**：不增加新的工具类型，在 FreeDraw 类型上默认启用

## 核心组件

### 1. FreeDrawTool 增强

#### 新增数据结构
```cpp
struct SpeedPoint {
    QPointF point;          // 点坐标
    qreal speed;            // 移动速度 (像素/毫秒)
    qreal width;            // 计算出的线宽
    qint64 timestamp;       // 时间戳
};
```

#### 关键方法
- `startSpeedStroke()`: 开始速度笔锋绘制
- `addSpeedPoint()`: 添加速度点并计算线宽
- `finishSpeedStroke()`: 完成笔锋绘制
- `getSpeedStrokePath()`: 获取笔锋路径
- `getSpeedStrokeBounds()`: 获取笔锋边界

### 2. OptimizedDrawingState 集成

#### 新增功能
- 集成 `FreeDrawTool` 实例
- 速度笔锋开关控制
- 自动路径切换（普通路径 vs 速度笔锋路径）

#### 配置选项
```cpp
// 启用/禁用速度笔锋
void setSpeedStrokeEnabled(bool enabled);
bool isSpeedStrokeEnabled() const;
```

## 算法原理

### 1. 速度计算
```cpp
qreal speed = distance / timeDelta;  // 像素/毫秒
speed = qBound(MIN_SPEED, speed, MAX_SPEED);  // 限制范围
```

### 2. 宽度映射
```cpp
// 速度越快，线条越细（反比例关系）
qreal speedRatio = (speed - MIN_SPEED) / (MAX_SPEED - MIN_SPEED);
qreal widthRatio = 1.0 - speedRatio;
widthRatio = MIN_WIDTH_RATIO + widthRatio * (MAX_WIDTH_RATIO - MIN_WIDTH_RATIO);
qreal finalWidth = baseWidth * widthRatio;
```

### 3. 平滑处理
```cpp
// 指数平滑减少宽度突变
qreal smoothedWidth = lastWidth * (1.0 - SMOOTHING) + newWidth * SMOOTHING;
```

### 4. 路径生成
- 计算每个点的垂直方向向量
- 根据线宽生成左右边界点
- 构建闭合的笔锋轮廓路径

## 配置参数

### 速度范围
- `MIN_SPEED = 0.1` 像素/毫秒
- `MAX_SPEED = 5.0` 像素/毫秒

### 宽度比例
- `MIN_WIDTH_RATIO = 0.2` (最细为基础宽度的 20%)
- `MAX_WIDTH_RATIO = 1.0` (最粗为基础宽度的 100%)

### 平滑系数
- `SPEED_SMOOTHING = 0.3` (速度平滑)
- `WIDTH_SMOOTHING = 0.4` (宽度平滑)

## 使用方法

### 1. 基本使用
```cpp
// 创建绘制状态
OptimizedDrawingState drawingState;
drawingState.setToolType(ToolType::FreeDraw);

// 设置画笔（基础线宽）
QPen pen(Qt::black, 8.0);
drawingState.setPen(pen);

// 启用速度笔锋（默认已启用）
drawingState.setSpeedStrokeEnabled(true);

// 开始绘制
drawingState.startDrawing(startPoint);
drawingState.continueDrawing(point1);
drawingState.continueDrawing(point2);
// ...
drawingState.finishDrawing();

// 获取最终路径
QPainterPath path = drawingState.getCurrentPath();
```

### 2. 渲染路径
```cpp
void paintEvent(QPaintEvent* event) {
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 绘制速度笔锋路径
    QPainterPath strokePath = drawingState.getCurrentPath();
    painter.setPen(Qt::NoPen);
    painter.setBrush(QBrush(Qt::black));
    painter.drawPath(strokePath);
}
```

## 性能优化

1. **增量计算**：只计算新增点的速度和宽度
2. **批量处理**：使用向量操作生成路径轮廓
3. **缓存机制**：复用 OptimizedDrawingState 的缓存系统
4. **平滑算法**：减少不必要的路径重建

## 兼容性

- **向后兼容**：不影响现有的绘制功能
- **可选功能**：可通过开关控制启用/禁用
- **工具无关**：仅对 FreeDraw 工具生效，其他工具不受影响

## 示例代码

参见 `examples/SpeedStrokeExample.cpp` 获取完整的使用示例。

## 调试和测试

### 调试信息
可以通过以下方式获取调试信息：
```cpp
// 检查是否有速度笔锋数据
bool hasStroke = freeDrawTool.hasSpeedStroke();

// 获取速度点数量
int pointCount = freeDrawTool.getSpeedPoints().size();
```

### 性能测试
使用 `DrawingPerformanceProfiler` 监控性能：
```cpp
DRAWING_TIMER("SpeedStroke");
// 速度笔锋相关代码
```

## 未来扩展

1. **压感支持**：集成触控笔压感数据
2. **自定义曲线**：允许用户自定义速度-宽度映射曲线
3. **材质效果**：模拟不同笔刷材质的效果
4. **多点触控**：支持多指同时绘制的速度笔锋
