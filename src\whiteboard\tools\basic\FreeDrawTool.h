#ifndef FREEDRAWTOOL_H
#define FREEDRAWTOOL_H

#include "../AbstractShapeTool.h"
#include <QElapsedTimer>
#include <QPointF>
#include <QVector>

/**
 * @brief 自由绘制工具
 *
 * 特点：
 * - 支持连续路径绘制
 * - 支持速度笔锋效果
 * - 适用于手写、涂鸦等场景
 */
class FreeDrawTool : public AbstractShapeTool
{
public:
    /**
     * @brief 速度点数据结构
     */
    struct SpeedPoint {
        QPointF point;          // 点坐标
        qreal speed;            // 移动速度 (像素/毫秒)
        qreal width;            // 计算出的线宽
        qint64 timestamp;       // 时间戳

        SpeedPoint() : speed(0.0), width(1.0), timestamp(0) {}
        SpeedPoint(const QPointF& p, qreal s, qreal w, qint64 t)
            : point(p), speed(s), width(w), timestamp(t) {}
    };

public:
    FreeDrawTool();
    ~FreeDrawTool() = default;

    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;

    // 速度笔锋相关接口
    void startSpeedStroke(const QPointF& startPoint, qreal baseWidth);
    void addSpeedPoint(const QPointF& point, qreal baseWidth);
    void finishSpeedStroke();
    QPainterPath getSpeedStrokePath() const;
    QRectF getSpeedStrokeBounds() const;
    bool hasSpeedStroke() const { return !m_speedPoints.isEmpty(); }

private:
    // 速度计算
    qreal calculateSpeed(const QPointF& currentPoint, const QPointF& lastPoint, qint64 timeDelta) const;
    qreal calculateWidthFromSpeed(qreal speed, qreal baseWidth) const;
    qreal smoothWidth(qreal newWidth, qreal lastWidth) const;

    // 路径生成
    QPainterPath generateSpeedStrokePath() const;
    QVector<QPointF> generateStrokeOutline(const QVector<SpeedPoint>& points) const;

private:
    // 速度笔锋数据
    QVector<SpeedPoint> m_speedPoints;      // 速度点序列
    QElapsedTimer m_strokeTimer;            // 笔画计时器
    qint64 m_lastTimestamp;                 // 上一个点的时间戳

    // 速度笔锋配置
    static constexpr qreal MIN_SPEED = 0.1;         // 最小速度 (像素/毫秒)
    static constexpr qreal MAX_SPEED = 5.0;         // 最大速度 (像素/毫秒)
    static constexpr qreal MIN_WIDTH_RATIO = 0.2;   // 最小宽度比例
    static constexpr qreal MAX_WIDTH_RATIO = 1.0;   // 最大宽度比例
    static constexpr qreal SPEED_SMOOTHING = 0.3;   // 速度平滑系数
    static constexpr qreal WIDTH_SMOOTHING = 0.4;   // 宽度平滑系数
};

#endif // FREEDRAWTOOL_H
