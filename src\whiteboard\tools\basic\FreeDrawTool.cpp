#include "FreeDrawTool.h"

FreeDrawTool::FreeDrawTool()
    : AbstractShapeTool(ToolType::FreeDraw)
{
    // 设置默认笔锋配置
    m_brushRenderer.setBrushConfig(getDefaultBrushConfig());
}

QPainterPath FreeDrawTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    if (m_brushStrokeEnabled && m_brushRenderer.hasStroke()) {
        // 笔锋模式：返回笔锋路径
        return m_brushRenderer.getCurrentStrokePath();
    } else {
        // 普通模式：返回简单线段
        QPainterPath path;
        path.moveTo(startPoint);
        path.lineTo(currentPoint);
        return path;
    }
}

QRectF FreeDrawTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    if (m_brushStrokeEnabled && m_brushRenderer.hasStroke()) {
        // 笔锋模式：返回笔锋边界
        return m_brushRenderer.getCurrentBounds();
    } else {
        // 普通模式：返回简单边界
        return QRectF(startPoint, currentPoint).normalized();
    }
}

QString FreeDrawTool::getToolName() const
{
    return "FreeDraw";
}
