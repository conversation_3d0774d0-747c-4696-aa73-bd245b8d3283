#include "FreeDrawTool.h"
#include <QtMath>
#include <QDebug>

FreeDrawTool::FreeDrawTool()
    : AbstractShapeTool(ToolType::FreeDraw)
    , m_lastTimestamp(0)
{
}

QPainterPath FreeDrawTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    // 如果有速度笔锋数据，返回速度笔锋路径
    if (hasSpeedStroke()) {
        return getSpeedStrokePath();
    }

    // 否则返回简单路径
    QPainterPath path;
    path.moveTo(startPoint);
    path.lineTo(currentPoint);
    return path;
}

QRectF FreeDrawTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    // 如果有速度笔锋数据，返回速度笔锋边界
    if (hasSpeedStroke()) {
        return getSpeedStrokeBounds();
    }

    // 否则返回简单边界
    return QRectF(startPoint, currentPoint).normalized();
}

QString FreeDrawTool::getToolName() const
{
    return "FreeDraw";
}

void FreeDrawTool::startSpeedStroke(const QPointF& startPoint, qreal baseWidth)
{
    m_speedPoints.clear();
    m_strokeTimer.start();
    m_lastTimestamp = 0;

    // 添加起始点
    SpeedPoint startSpeedPoint(startPoint, 0.0, baseWidth, 0);
    m_speedPoints.append(startSpeedPoint);
}

void FreeDrawTool::addSpeedPoint(const QPointF& point, qreal baseWidth)
{
    if (m_speedPoints.isEmpty()) {
        startSpeedStroke(point, baseWidth);
        return;
    }

    qint64 currentTime = m_strokeTimer.elapsed();
    qint64 timeDelta = currentTime - m_lastTimestamp;

    // 避免时间间隔过小导致的计算问题
    if (timeDelta < 1) {
        timeDelta = 1;
    }

    const SpeedPoint& lastPoint = m_speedPoints.last();

    // 计算速度
    qreal speed = calculateSpeed(point, lastPoint.point, timeDelta);

    // 根据速度计算宽度
    qreal rawWidth = calculateWidthFromSpeed(speed, baseWidth);

    // 平滑宽度变化
    qreal smoothedWidth = smoothWidth(rawWidth, lastPoint.width);

    // 添加新的速度点
    SpeedPoint newPoint(point, speed, smoothedWidth, currentTime);
    m_speedPoints.append(newPoint);

    m_lastTimestamp = currentTime;
}

void FreeDrawTool::finishSpeedStroke()
{
    // 笔画结束，可以在这里做一些清理或优化工作
    // 目前保持速度点数据，供路径生成使用
}

QPainterPath FreeDrawTool::getSpeedStrokePath() const
{
    return generateSpeedStrokePath();
}

QRectF FreeDrawTool::getSpeedStrokeBounds() const
{
    if (m_speedPoints.isEmpty()) {
        return QRectF();
    }

    QPainterPath path = generateSpeedStrokePath();
    return path.boundingRect();
}

qreal FreeDrawTool::calculateSpeed(const QPointF& currentPoint, const QPointF& lastPoint, qint64 timeDelta) const
{
    if (timeDelta <= 0) {
        return 0.0;
    }

    // 计算距离
    qreal distance = QLineF(lastPoint, currentPoint).length();

    // 计算速度 (像素/毫秒)
    qreal speed = distance / timeDelta;

    // 限制速度范围
    return qBound(MIN_SPEED, speed, MAX_SPEED);
}

qreal FreeDrawTool::calculateWidthFromSpeed(qreal speed, qreal baseWidth) const
{
    // 速度越快，线条越细
    // 使用反比例关系，但保持在合理范围内
    qreal speedRatio = (speed - MIN_SPEED) / (MAX_SPEED - MIN_SPEED);
    speedRatio = qBound(0.0, speedRatio, 1.0);

    // 反转比例：速度快时宽度小
    qreal widthRatio = 1.0 - speedRatio;

    // 应用最小和最大宽度比例
    widthRatio = MIN_WIDTH_RATIO + widthRatio * (MAX_WIDTH_RATIO - MIN_WIDTH_RATIO);

    return baseWidth * widthRatio;
}

qreal FreeDrawTool::smoothWidth(qreal newWidth, qreal lastWidth) const
{
    // 使用指数平滑来减少宽度的突变
    return lastWidth * (1.0 - WIDTH_SMOOTHING) + newWidth * WIDTH_SMOOTHING;
}

QPainterPath FreeDrawTool::generateSpeedStrokePath() const
{
    if (m_speedPoints.size() < 2) {
        return QPainterPath();
    }

    // 生成笔锋轮廓
    QVector<QPointF> outline = generateStrokeOutline(m_speedPoints);

    if (outline.isEmpty()) {
        // 如果轮廓生成失败，回退到简单路径
        QPainterPath fallbackPath;
        fallbackPath.moveTo(m_speedPoints.first().point);
        for (int i = 1; i < m_speedPoints.size(); ++i) {
            fallbackPath.lineTo(m_speedPoints[i].point);
        }
        return fallbackPath;
    }

    // 创建路径
    QPainterPath path;
    if (!outline.isEmpty()) {
        path.moveTo(outline.first());
        for (int i = 1; i < outline.size(); ++i) {
            path.lineTo(outline[i]);
        }
        path.closeSubpath();
    }

    return path;
}

QVector<QPointF> FreeDrawTool::generateStrokeOutline(const QVector<SpeedPoint>& points) const
{
    QVector<QPointF> outline;

    if (points.size() < 2) {
        return outline;
    }

    QVector<QPointF> leftSide;
    QVector<QPointF> rightSide;

    for (int i = 0; i < points.size(); ++i) {
        const SpeedPoint& current = points[i];

        QPointF direction;
        if (i == 0) {
            // 第一个点：使用到下一个点的方向
            direction = points[i + 1].point - current.point;
        } else if (i == points.size() - 1) {
            // 最后一个点：使用从上一个点的方向
            direction = current.point - points[i - 1].point;
        } else {
            // 中间点：使用平均方向
            QPointF dir1 = current.point - points[i - 1].point;
            QPointF dir2 = points[i + 1].point - current.point;
            direction = (dir1 + dir2) / 2.0;
        }

        // 标准化方向向量
        qreal length = qSqrt(direction.x() * direction.x() + direction.y() * direction.y());
        if (length > 0.001) {
            direction /= length;
        } else {
            direction = QPointF(1.0, 0.0); // 默认方向
        }

        // 计算垂直方向
        QPointF perpendicular(-direction.y(), direction.x());

        // 计算左右边界点
        qreal halfWidth = current.width / 2.0;
        QPointF leftPoint = current.point + perpendicular * halfWidth;
        QPointF rightPoint = current.point - perpendicular * halfWidth;

        leftSide.append(leftPoint);
        rightSide.prepend(rightPoint); // 反向添加右侧点
    }

    // 合并左右两侧形成闭合轮廓
    outline = leftSide + rightSide;

    return outline;
}
